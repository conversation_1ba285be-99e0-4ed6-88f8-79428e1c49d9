<?php
// Simple test script to check for basic PHP issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "PHP Version: " . PHP_VERSION . "\n";
echo "Testing basic functionality...\n";

// Test autoloader
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    echo "✓ Vendor autoload exists\n";
    try {
        require_once __DIR__ . '/vendor/autoload.php';
        echo "✓ Vendor autoload loaded successfully\n";
    } catch (Exception $e) {
        echo "✗ Vendor autoload error: " . $e->getMessage() . "\n";
    }
} else {
    echo "✗ Vendor autoload not found\n";
}

// Test third party node
if (file_exists(__DIR__ . '/third_party/node.php')) {
    echo "✓ Third party node exists\n";
    try {
        require_once __DIR__ . '/third_party/node.php';
        echo "✓ Third party node loaded successfully\n";
        echo "REG_PROD_POINT: " . (defined('REG_PROD_POINT') ? REG_PROD_POINT : 'Not defined') . "\n";
    } catch (Exception $e) {
        echo "✗ Third party node error: " . $e->getMessage() . "\n";
    }
} else {
    echo "✗ Third party node not found\n";
}

// Test core Apiinit
if (file_exists(__DIR__ . '/core/Apiinit.php')) {
    echo "✓ Core Apiinit exists\n";
    try {
        require_once __DIR__ . '/core/Apiinit.php';
        echo "✓ Core Apiinit loaded successfully\n";
    } catch (Exception $e) {
        echo "✗ Core Apiinit error: " . $e->getMessage() . "\n";
    }
} else {
    echo "✗ Core Apiinit not found\n";
}

// Test install.php
if (file_exists(__DIR__ . '/install.php')) {
    echo "✓ Install.php exists\n";
} else {
    echo "✗ Install.php not found\n";
}

// Test updates.php
if (file_exists(__DIR__ . '/updates.php')) {
    echo "✓ Updates.php exists\n";
} else {
    echo "✗ Updates.php not found\n";
}

echo "Test completed.\n";
?>
