<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
Module Name: WhatsApp Official Cloud API Chat & Marketing module for Perfex CRM
Description: Schedule programmatic marketing actions and  interact with your clients in realtime, through admin area of Perfex CRM.
Version: 1.6.1
Requires at least: 3.2.*
Module URI: https://codecanyon.net/item/whatsapp-cloud-api-interaction-module-for-perfex-crm/52004114
*/

define('WHATSAPP_MODULE_NAME', 'whatsapp');
define('WHATSAPP_MODULE', 'whatsapp');
define('WHATSAPP_UPDATE_URI', 'whatsapp');
define('WHATSAPP_VERSION', '1.6.0');

// Basic error handling
try {
    // Register language files
    register_language_files(WHATSAPP_MODULE_NAME, [WHATSAPP_MODULE_NAME]);

    // Define constants for upload folders
    define('WHATSAPP_MODULE_UPLOAD_FOLDER', 'uploads/' . WHATSAPP_MODULE_NAME);
    define('WHATSAPP_MODULE_UPLOAD_URL', (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . '/' . WHATSAPP_MODULE_UPLOAD_FOLDER . '/');

    // Simple activation hook
    register_activation_hook(WHATSAPP_MODULE_NAME, 'whatsapp_simple_activation_hook');

    function whatsapp_simple_activation_hook()
    {
        try {
            // Just add a simple option to test activation
            if (function_exists('add_option')) {
                add_option('whatsapp_activated', '1');
            }
            return true;
        } catch (Exception $e) {
            error_log('WhatsApp activation error: ' . $e->getMessage());
            return false;
        }
    }

} catch (Exception $e) {
    error_log('WhatsApp module loading error: ' . $e->getMessage());
} catch (Error $e) {
    error_log('WhatsApp module fatal error: ' . $e->getMessage());
}
